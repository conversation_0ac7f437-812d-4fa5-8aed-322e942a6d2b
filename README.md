# WhatsApp AI Bot

A WhatsApp bot that responds to mentions with AI-generated content, with support for Groq, Google and OpenAI models.

## Features

- Integrates with WhatsApp using the `whatsapp-web.js` library
- Responds when mentioned in group chats or in direct messages
- Generates responses using Groq, Google, or OpenAI models
- Can reply with voice notes for a more natural interaction
- Vision capabilities: Can analyze images sent in the chat
- Includes rate limiting to prevent API abuse
- Formats responses appropriately for WhatsApp
- Web interface for easy QR code scanning
- Runs in Docker for easy deployment

## Prerequisites

- Docker and Docker Compose
- An API key for Groq, Google, or OpenAI
- A WhatsApp account

## Setup

1. Clone the repository or download the source code
2. Create a `.env` file based on `.env.example` and add your API key(s). You only need to provide a key for the service you intend to use. For voice notes, `OPENAI_API_KEY` is required.

```
# --- Provider API Keys (at least one is required) ---
GROQ_API_KEY=
GOOGLE_GENERATIVE_AI_API_KEY=
OPENAI_API_KEY=

# --- Model Configuration ---
# MODEL_PROVIDER: "groq", "gemini", or "openai" (defaults to "gemini")
# MODEL: (Optional) Specify a model from the chosen provider.
# e.g., for groq: "llama-3.3-70b-versatile"
# e.g., for gemini: "gemini-2.0-flash"
# e.g., for openai: "gpt-4o"
MODEL_PROVIDER=gemini
MODEL=

# --- Bot Configuration ---
BOT_NAME=Nonce
GROUP_ONLY=false # Set to true to only respond in groups

# Rate Limiting
RATE_LIMIT=5
RATE_LIMIT_WINDOW=60000

# Web Server Configuration
WEB_PORT=3280

# Puppeteer Configuration (for Docker)
PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
```

3. Build and start the Docker container:

```bash
docker-compose up --build
```

4. Scan the QR code with your WhatsApp to log in
   - The QR code will be displayed in the console
   - You can also visit `http://localhost:3280` for a web interface to scan the QR code

## Usage

The bot supports multiple ways to get its attention:

### 1. Native WhatsApp Mentions (Recommended)

- Use WhatsApp's native mention feature by typing `@` and selecting the bot from the list
- This is the most reliable method and works in all group chats

### 2. Text-based Mentions

- Mention the bot using `@AIAssistant` (or whatever name you configured)
- This works as a fallback when native mentions aren't available

### 3. Direct Name Usage

- Start your message with the bot's name: `AIAssistant: What's the capital of France?`
- This works for quick questions without using @ symbols

### 4. Voice Note Replies

- To receive a reply as a voice note, start your message with the `!voice` command when mentioning the bot.
- This feature uses OpenAI's Text-to-Speech model, so an `OPENAI_API_KEY` is required.

### 5. Image Generation

- To generate an image, include the phrase "generate an image" in your message when mentioning the bot.
- This feature uses OpenAI's GPT-Image-1 model, so an `OPENAI_API_KEY` is required.

Examples:

```
You: @AIAssistant What's the capital of France?
Bot: The capital of France is Paris.

You: @AIAssistant !voice Tell me a fun fact.
Bot: [Sends a voice note with a fun fact]

You: @AIAssistant generate an image of a sunset over mountains
Bot: [Sends a generated image with the caption "Here's your generated image: sunset over mountains"]
```

**Note**: The bot will respond with appropriate reactions:

- 🤔 while processing your request
- ✅ when the response is sent successfully
- ❌ if an error occurs

## Configuration Options

- `BOT_NAME`: The name the bot responds to when mentioned
- `GROUP_ONLY`: If true, the bot only responds in group chats
- `GROQ_API_KEY`, `GOOGLE_GENERATIVE_AI_API_KEY`, `OPENAI_API_KEY`: API keys for the respective services
- `MODEL_PROVIDER`: The AI provider to use (`groq`, `gemini`, `openai`)
- `MODEL`: The specific model to use from the chosen provider
- `MAX_TOKENS`: Maximum tokens for each response
- `RATE_LIMIT`: Maximum number of requests allowed per time window
- `RATE_LIMIT_WINDOW`: Time window for rate limiting in milliseconds
- `WEB_PORT`: Port for the web interface (default: 3280)

**Note**: The `OPENAI_API_KEY` is required for voice notes and image generation features, regardless of your chosen `MODEL_PROVIDER`.

## Docker Volumes

The bot uses two Docker volumes:

- `.wwebjs_auth`: Stores WhatsApp session data
- `.env`: Contains environment variables

## License

MIT
