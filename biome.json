{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "complexity": {"noExtraBooleanCast": "error", "noMultipleSpacesInRegularExpressionLiterals": "error", "noUselessCatch": "error", "noUselessTypeConstraint": "error", "noWith": "error"}, "correctness": {"noConstAssign": "error", "noConstantCondition": "error", "noEmptyCharacterClassInRegex": "error", "noEmptyPattern": "error", "noGlobalObjectCalls": "error", "noInvalidConstructorSuper": "error", "noInvalidNewBuiltin": "error", "noNonoctalDecimalEscape": "error", "noPrecisionLoss": "error", "noSelfAssign": "error", "noSetterReturn": "error", "noSwitchDeclarations": "error", "noUndeclaredVariables": "error", "noUnreachable": "error", "noUnreachableSuper": "error", "useIsNan": "error", "useValidForDirection": "error", "useYield": "error"}, "security": {"noDangerouslySetInnerHtml": "error", "noGlobalEval": "error"}, "style": {"noCommaOperator": "error", "noImplicitBoolean": "off", "noNegationElse": "off", "useConst": "error", "useDefaultParameterLast": "error"}, "suspicious": {"noArrayIndexKey": "warn", "noAssignInExpressions": "error", "noCatchAssign": "error", "noClassAssign": "error", "noCompareNegZero": "error", "noControlCharactersInRegex": "error", "noDebugger": "error", "noDuplicateCase": "error", "noDuplicateClassMembers": "error", "noDuplicateObjectKeys": "error", "noDuplicateParameters": "error", "noEmptyBlockStatements": "error", "noExplicitAny": "warn", "noFallthroughSwitchClause": "error", "noFunctionAssign": "error", "noGlobalAssign": "error", "noImportAssign": "error", "noMisleadingCharacterClass": "error", "noPrototypeBuiltins": "error", "noRedeclare": "error", "noShadowRestrictedNames": "error", "noUnsafeNegation": "error", "useGetterReturn": "error", "useValidTypeof": "error"}}}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 80, "attributePosition": "auto"}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "always", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false, "quoteStyle": "double", "attributePosition": "auto"}}, "files": {"include": ["src/**/*.js", "*.js"], "ignore": ["node_modules/**", "dist/**", ".wwebjs_auth/**"]}}