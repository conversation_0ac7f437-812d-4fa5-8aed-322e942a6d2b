const chalk = require("chalk");

/**
 * Logger utility for console output
 */
const logger = {
  /**
   * Log an info message
   * @param {string} message - The message to log
   * @param {any} data - Optional data to log
   */
  info: (message, data) => {
    const timestamp = new Date().toISOString();
    console.log(`${chalk.blue("[INFO]")} ${chalk.gray(timestamp)} ${message}`);
    if (data) console.log(data);
  },

  /**
   * Log a success message
   * @param {string} message - The message to log
   * @param {any} data - Optional data to log
   */
  success: (message, data) => {
    const timestamp = new Date().toISOString();
    console.log(
      `${chalk.green("[SUCCESS]")} ${chalk.gray(timestamp)} ${message}`
    );
    if (data) console.log(data);
  },

  /**
   * Log a warning message
   * @param {string} message - The message to log
   * @param {any} data - Optional data to log
   */
  warn: (message, data) => {
    const timestamp = new Date().toISOString();
    console.log(
      `${chalk.yellow("[WARNING]")} ${chalk.gray(timestamp)} ${message}`
    );
    if (data) console.log(data);
  },

  /**
   * Log an error message
   * @param {string} message - The message to log
   * @param {any} error - Optional error to log
   */
  error: (message, error) => {
    const timestamp = new Date().toISOString();
    console.log(`${chalk.red("[ERROR]")} ${chalk.gray(timestamp)} ${message}`);
    if (error) {
      if (error instanceof Error) {
        console.log(chalk.red(error.stack || error.message));
      } else {
        console.log(error);
      }
    }
  },
};

module.exports = logger;
