/**
 * Formats the response message
 * @param {string} text - The raw response text
 * @returns {string} - The formatted message
 */
const formatMessage = (text) => {
  if (!text) return "I don't have a response for that.";

  // Ensure message isn't too long for WhatsApp
  const maxLength = 4096;
  let formattedText = text;

  if (formattedText.length > maxLength) {
    formattedText = `${formattedText.substring(0, maxLength - 3)}...`;
  }

  // Format code blocks if present
  formattedText = formattedText.replace(/```([\s\S]*?)```/g, (match, code) => {
    return `*Code:*\n\`\`\`\n${code.trim()}\n\`\`\``;
  });

  // Format bold text
  formattedText = formattedText.replace(/\*\*(.*?)\*\*/g, "*$1*");

  return formattedText;
};

module.exports = {
  formatMessage,
};
