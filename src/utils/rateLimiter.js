/**
 * Simple rate limiter implementation
 */
class RateLimiter {
  constructor(limit, window) {
    this.limit = limit;
    this.window = window;
    this.clients = new Map();
  }

  /**
   * Try to acquire a rate limit token
   * @param {string} clientId - The client ID (e.g., chat ID)
   * @returns {boolean} - Whether the request is allowed
   */
  tryAcquire(clientId) {
    const now = Date.now();

    if (!this.clients.has(clientId)) {
      this.clients.set(clientId, {
        count: 1,
        resetTime: now + this.window,
      });
      return true;
    }

    const client = this.clients.get(clientId);

    // Reset counter if the window has passed
    if (now > client.resetTime) {
      client.count = 1;
      client.resetTime = now + this.window;
      return true;
    }

    // Check if limit is reached
    if (client.count >= this.limit) {
      return false;
    }

    // Increment counter
    client.count++;
    return true;
  }
}

module.exports = {
  RateLimiter,
};
