/**
 * Resolves the best display name for a contact, prioritizing human-readable names over phone numbers
 * @param {Object} contact - The WhatsApp contact object
 * @param {string} fallbackId - Fallback ID if no contact info is available
 * @returns {Promise<string>} - The best available display name
 */
const getContactDisplayName = async (contact, fallbackId = null) => {
  if (!contact) {
    return fallbackId || "Unknown";
  }

  try {
    // Priority order: pushname (WhatsApp display name) > name (contact name) > formatted number > raw number
    if (contact.pushname?.trim()) {
      return contact.pushname.trim();
    }

    if (contact.name?.trim()) {
      return contact.name.trim();
    }

    // Try to get a formatted version of the number if available
    if (contact.formattedName?.trim()) {
      return contact.formattedName.trim();
    }

    // If we have the number, try to format it better
    if (contact.number) {
      // Format phone number to be more readable (add spaces/dashes)
      const number = contact.number.replace(/^\+/, "");
      if (number.length >= 10) {
        // Format as +XX XXX XXX XXXX for better readability
        const formatted = number.replace(
          /(\d{2})(\d{3})(\d{3})(\d+)/,
          "+$1 $2 $3 $4"
        );
        return formatted;
      }
      return `+${number}`;
    }

    // Last resort fallbacks
    if (contact.id?.user) {
      const number = contact.id.user;
      if (number.length >= 10) {
        const formatted = number.replace(
          /(\d{2})(\d{3})(\d{3})(\d+)/,
          "+$1 $2 $3 $4"
        );
        return formatted;
      }
      return `+${number}`;
    }

    return fallbackId || "Unknown User";
  } catch (error) {
    console.error("Error resolving contact display name:", error);
    return fallbackId || "Unknown";
  }
};

/**
 * Formats a timestamp into a readable date and time string
 * @param {number} timestamp - Unix timestamp (in seconds or milliseconds)
 * @returns {string} - Formatted date and time string
 */
const formatMessageDateTime = (timestamp) => {
  try {
    // WhatsApp timestamps are typically in seconds, but sometimes in milliseconds
    // If timestamp is less than 1e12 (year 2001 in milliseconds), it's likely in seconds
    const date = new Date(timestamp < 1e12 ? timestamp * 1000 : timestamp);

    // Format as: "Dec 15, 2023 at 2:30 PM"
    const options = {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    };

    return date.toLocaleString("en-US", options);
  } catch (error) {
    console.error("Error formatting timestamp:", error);
    return "Unknown time";
  }
};

/**
 * Checks if the bot is mentioned in the message
 * @param {Object} message - The WhatsApp message object
 * @param {string} botName - The bot's name (fallback for text-based detection)
 * @param {Object} client - The WhatsApp client instance
 * @returns {Promise<boolean>} - Whether the bot is mentioned
 */
const isMentioned = async (message, botName, client = null) => {
  if (!message) return false;

  try {
    // First, try to get actual WhatsApp mentions using mentionedIds array
    if (message.mentionedIds && message.mentionedIds.length > 0) {
      // Get the bot's own ID from the client
      if (client?.info?.wid) {
        const botId = client.info.wid._serialized;

        // Check if the bot's ID is in the mentioned IDs
        const botMentioned = message.mentionedIds.includes(botId);
        if (botMentioned) {
          console.log("✅ Bot mentioned via native WhatsApp mention");
          return true;
        }
      }
    }

    // Alternative: try using getMentions() method
    try {
      const mentions = await message.getMentions();
      if (mentions && mentions.length > 0 && client && client.info) {
        const botNumber = client.info.wid.user;
        const botId = client.info.wid._serialized;

        const botMentioned = mentions.some((contact) => {
          return (
            contact.id.user === botNumber || contact.id._serialized === botId
          );
        });

        if (botMentioned) {
          console.log("✅ Bot mentioned via getMentions() method");
          return true;
        }
      }
    } catch (mentionError) {
      // Continue to fallback methods
    }

    // Fallback to text-based detection for backward compatibility
    const messageText = message.body;
    if (!messageText) return false;

    // Check for direct @ mention in text
    if (messageText.includes(`@${botName}`)) {
      console.log("✅ Bot mentioned via text-based @botname");
      return true;
    }

    // Check for the bot name at the beginning of the message
    const nameRegex = new RegExp(`^${botName}[,:.!?\\s]`, "i");
    if (nameRegex.test(messageText)) {
      console.log("✅ Bot mentioned via name at start of message");
      return true;
    }

    return false;
  } catch (error) {
    console.error("Error checking mentions:", error);

    // Fallback to text-based detection if there's an error
    const messageText = message.body || "";
    if (messageText.includes(`@${botName}`)) return true;

    const nameRegex = new RegExp(`^${botName}[,:.!?\\s]`, "i");
    return nameRegex.test(messageText);
  }
};

/**
 * Extracts the question from the message, removing mentions and bot name references
 * @param {Object} message - The WhatsApp message object
 * @param {string} botName - The bot's name
 * @returns {Promise<string|null>} - The extracted question or null if none found
 */
const extractQuestion = async (message, botName) => {
  if (!message || !message.body) return null;

  try {
    let question = message.body;

    // Remove WhatsApp mention patterns with Unicode characters
    // WhatsApp mentions can include invisible Unicode characters like ⁨ and ⁩
    // Pattern matches: @⁨name⁩, @[phone_number], @name with Unicode characters
    question = question
      .replace(
        /@[\u2068\u2069\u200E\u200F]*[^@\s]*[\u2068\u2069\u200E\u200F]*/g,
        ""
      )
      .trim();

    // Remove additional WhatsApp mention patterns
    // WhatsApp mentions appear as @[phone_number] in the message text
    question = question.replace(/@\d+/g, "").trim();

    // Also try to remove mentions using the getMentions method
    try {
      const mentions = await message.getMentions();
      if (mentions && mentions.length > 0) {
        // Remove each mentioned contact's number from the text
        for (const contact of mentions) {
          if (contact.id?.user) {
            const phonePattern = new RegExp(`@${contact.id.user}`, "g");
            question = question.replace(phonePattern, "").trim();
          }
          // Also try to remove by name if available
          if (contact.pushname || contact.name) {
            const nameToRemove = contact.pushname || contact.name;
            // Create a pattern that handles Unicode characters around the name
            const namePattern = new RegExp(
              `@[\u2068\u2069\u200E\u200F]*${nameToRemove.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}[\u2068\u2069\u200E\u200F]*`,
              "gi"
            );
            question = question.replace(namePattern, "").trim();
          }
        }
      }
    } catch (mentionError) {
      // Continue with other cleaning methods
    }

    // Remove text-based @ mentions
    question = question.replace(new RegExp(`@${botName}`, "gi"), "").trim();

    // Remove bot name at the beginning
    const nameRegex = new RegExp(`^${botName}[,:.!?\\s]`, "i");
    question = question.replace(nameRegex, "").trim();

    // Clean up extra whitespace and invisible Unicode characters
    question = question.replace(/[\u2068\u2069\u200E\u200F]/g, "").trim();
    question = question.replace(/\s+/g, " ").trim();

    return question || null;
  } catch (error) {
    console.error("Error extracting question:", error);

    // Fallback to simple text processing
    let question = message.body || "";

    // Enhanced fallback that also handles Unicode characters
    question = question
      .replace(
        /@[\u2068\u2069\u200E\u200F]*[^@\s]*[\u2068\u2069\u200E\u200F]*/g,
        ""
      )
      .trim();
    question = question.replace(/@\d+/g, "").trim(); // Remove @phone patterns
    question = question.replace(new RegExp(`@${botName}`, "gi"), "").trim();

    const nameRegex = new RegExp(`^${botName}[,:.!?\\s]`, "i");
    question = question.replace(nameRegex, "").trim();
    question = question.replace(/[\u2068\u2069\u200E\u200F]/g, "").trim();
    question = question.replace(/\s+/g, " ").trim();

    return question || null;
  }
};

/**
 * Fetches and formats previous messages from a chat to provide context
 * @param {Object} chat - The WhatsApp chat object
 * @param {string} botName - The bot's name to identify bot messages
 * @param {number} limit - Number of previous messages to fetch (default: 10)
 * @param {Object} currentMessage - The current message to exclude from context (optional)
 * @returns {Promise<Array>} - Array of formatted message objects for context
 */
const getMessageContext = async (
  chat,
  botName,
  limit = "10",
  currentMessage = null
) => {
  try {
    if (!chat || typeof chat.fetchMessages !== "function") {
      return [];
    }

    // Fetch more messages than needed to account for filtering
    const messages = await chat.fetchMessages({
      limit: Number.parseInt(limit) + (currentMessage ? 1 : 0),
    });

    if (!messages || messages.length === 0) {
      return [];
    }

    // Filter out the current message if provided
    let filteredMessages = messages;
    if (currentMessage) {
      filteredMessages = messages.filter((msg) => {
        // Filter by message ID, timestamp, and content to avoid the current message
        return (
          msg.id._serialized !== currentMessage.id._serialized ||
          msg.timestamp !== currentMessage.timestamp ||
          msg.body !== currentMessage.body
        );
      });
    }

    // Check for memory reset message and filter out that message and any earlier messages
    const memoryResetText = "🧠 Memory reset. I am now lobotomised";
    let memoryResetTimestamp = null;

    // Find the most recent memory reset message
    for (const msg of filteredMessages) {
      if (msg.body && msg.body.includes(memoryResetText)) {
        memoryResetTimestamp = msg.timestamp;
        break; // Take the first (most recent) occurrence since messages are in reverse chronological order
      }
    }

    // If memory reset found, filter out that message and all earlier messages
    if (memoryResetTimestamp !== null) {
      filteredMessages = filteredMessages.filter((msg) => {
        return msg.timestamp > memoryResetTimestamp;
      });
    }

    // Take only the requested limit
    const previousMessages = filteredMessages.slice(0, Number.parseInt(limit));

    // Format messages in parallel to speed up contact fetching
    const contextPromises = previousMessages.map(async (msg) => {
      // Skip system messages, notifications, and empty messages
      if (!msg.body || msg.type !== "chat") {
        return null;
      }

      // Double-check: skip if this matches the current message content
      if (
        currentMessage &&
        msg.body === currentMessage.body &&
        Math.abs(msg.timestamp - currentMessage.timestamp) < 5000
      ) {
        // Within 5 seconds
        return null;
      }

      // Get the message author/sender info
      const contact = await msg.getContact();
      const senderName = await getContactDisplayName(contact);

      // Format the message content
      let content = msg.body.trim();

      // Check if this is a bot message
      const isFromBot =
        msg.fromMe || senderName?.toLowerCase().includes(botName.toLowerCase());

      // Get formatted date/time for the message
      const messageDateTime = formatMessageDateTime(msg.timestamp);

      // Add sender information and date/time for user messages (but not bot messages)
      if (!isFromBot) {
        content = `${senderName} [${messageDateTime}]:\n${content}`;
      }

      return {
        content: content,
        fromMe: isFromBot,
        timestamp: msg.timestamp,
        dateTime: messageDateTime,
        sender: senderName,
      };
    });

    const contextMessages = (await Promise.all(contextPromises)).filter(
      Boolean
    );

    return contextMessages;
  } catch (error) {
    console.error("Error fetching message context:", error);
    return [];
  }
};

module.exports = {
  isMentioned,
  extractQuestion,
  getMessageContext,
  getContactDisplayName,
  formatMessageDateTime,
};
