/**
 * Key Rotator utility for cycling through API keys when rate limits are encountered
 */
class KeyRotator {
  constructor() {
    this.apiKeys = {
      openai: this.parseApiKeys(
        process.env.OPENAI_API_KEYS || process.env.OPENAI_API_KEY
      ),
      groq: this.parseApiKeys(
        process.env.GROQ_API_KEYS || process.env.GROQ_API_KEY
      ),
      gemini: this.parseApiKeys(
        process.env.GEMINI_API_KEYS || process.env.GEMINI_API_KEY
      ),
    };

    this.currentKeyIndex = {
      openai: 0,
      groq: 0,
      gemini: 0,
    };

    // Track failed keys to avoid retrying them immediately
    this.failedKeys = {
      openai: new Set(),
      groq: new Set(),
      gemini: new Set(),
    };

    // Reset failed keys after 3 hours
    this.resetInterval = 3 * 60 * 60 * 1000; // 3 hours
    this.startFailedKeyResetTimer();
  }

  /**
   * Parse API keys from environment variable (supports comma-separated keys)
   * @param {string} keysString - Comma-separated API keys or single key
   * @returns {Array<string>} - Array of API keys
   */
  parseApiKeys(keysString) {
    if (!keysString) return [];
    return keysString
      .split(",")
      .map((key) => key.trim())
      .filter((key) => key.length > 0);
  }

  /**
   * Get the current API key for a provider
   * @param {string} provider - The API provider (openai, groq, gemini)
   * @returns {string|null} - The current API key or null if none available
   */
  getCurrentKey(provider) {
    const keys = this.apiKeys[provider];
    if (!keys || keys.length === 0) return null;

    const currentIndex = this.currentKeyIndex[provider];
    return keys[currentIndex];
  }

  /**
   * Get the next available API key for a provider
   * @param {string} provider - The API provider (openai, groq, gemini)
   * @returns {string|null} - The next available API key or null if none available
   */
  getNextKey(provider) {
    const keys = this.apiKeys[provider];
    if (!keys || keys.length === 0) return null;

    const failedKeys = this.failedKeys[provider];
    const startIndex = this.currentKeyIndex[provider];

    // Try to find a non-failed key
    for (let i = 0; i < keys.length; i++) {
      const nextIndex = (startIndex + i + 1) % keys.length;
      const nextKey = keys[nextIndex];

      if (!failedKeys.has(nextKey)) {
        this.currentKeyIndex[provider] = nextIndex;
        return nextKey;
      }
    }

    // If all keys have failed, return the next key anyway (they might have recovered)
    const nextIndex = (startIndex + 1) % keys.length;
    this.currentKeyIndex[provider] = nextIndex;
    return keys[nextIndex];
  }

  /**
   * Mark a key as failed for a provider
   * @param {string} provider - The API provider (openai, groq, gemini)
   * @param {string} key - The API key that failed
   */
  markKeyAsFailed(provider, key) {
    if (this.failedKeys[provider]) {
      this.failedKeys[provider].add(key);
    }
  }

  /**
   * Check if an error is a rate limit error
   * @param {Error} error - The error to check
   * @returns {boolean} - Whether the error is a rate limit error
   */
  isRateLimitError(error) {
    const errorMessage = error.message?.toLowerCase() || "";
    const errorCode = error.code || error.status || 0;

    // Common rate limit indicators
    const rateLimitKeywords = [
      "rate limit",
      "too many requests",
      "quota exceeded",
      "rate exceeded",
      "throttled",
      "rate_limit_exceeded",
      "quota_exceeded",
    ];

    const rateLimitCodes = [429, "RATE_LIMIT_EXCEEDED", "QUOTA_EXCEEDED"];

    return (
      rateLimitKeywords.some((keyword) => errorMessage.includes(keyword)) ||
      rateLimitCodes.includes(errorCode)
    );
  }

  /**
   * Execute an API call with automatic key rotation on rate limits
   * @param {string} provider - The API provider (openai, groq, gemini)
   * @param {Function} apiCallFunction - Function that makes the API call with the key
   * @param {number} maxRetries - Maximum number of retries (default: 3)
   * @returns {Promise<any>} - The API call result
   */
  async executeWithRotation(provider, apiCallFunction, maxRetries = 3) {
    let lastError;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      const currentKey =
        attempt === 0
          ? this.getCurrentKey(provider)
          : this.getNextKey(provider);

      if (!currentKey) {
        throw new Error(`No API keys available for provider: ${provider}`);
      }

      try {
        const result = await apiCallFunction(currentKey);
        return result;
      } catch (error) {
        lastError = error;

        if (this.isRateLimitError(error)) {
          console.warn(
            `Rate limit hit for ${provider} key ending in ...${currentKey.slice(-4)}. Rotating to next key.`
          );
          this.markKeyAsFailed(provider, currentKey);
        } else {
          // Non-rate-limit error, don't rotate key and re-throw immediately
          throw error;
        }
      }
    }

    // If we've exhausted all retries, throw the last error
    throw new Error(
      `Failed after ${maxRetries} attempts with key rotation for ${provider}: ${lastError.message}`
    );
  }

  /**
   * Start a timer to reset failed keys periodically
   */
  startFailedKeyResetTimer() {
    setInterval(() => {
      // Clear all failed keys to give them another chance
      for (const provider of Object.keys(this.failedKeys)) {
        this.failedKeys[provider].clear();
      }
      console.log("Reset failed API keys - giving them another chance");
    }, this.resetInterval);
  }

  /**
   * Get statistics about available keys and their status
   * @returns {Object} - Statistics object
   */
  getStats() {
    const stats = {};

    for (const provider of Object.keys(this.apiKeys)) {
      const totalKeys = this.apiKeys[provider].length;
      const failedKeys = this.failedKeys[provider].size;
      const availableKeys = totalKeys - failedKeys;

      stats[provider] = {
        total: totalKeys,
        failed: failedKeys,
        available: availableKeys,
        current: this.getCurrentKey(provider)?.slice(-4) || "none",
      };
    }

    return stats;
  }
}

// Create a singleton instance
const keyRotator = new KeyRotator();

module.exports = {
  keyRotator,
  KeyRotator,
};
