<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp LLM Bot - Management Console</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            min-height: calc(100vh - 40px);
        }
        
        @media (max-width: 768px) {
            .main-container {
                grid-template-columns: 1fr;
                gap: 15px;
                padding: 10px;
            }
            body {
                padding: 10px;
            }
        }
        
        .panel {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
            overflow-y: auto;
        }
        
        .panel h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2em;
            font-weight: 300;
        }
        
        .panel h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
            font-weight: 400;
        }
        
        .subtitle {
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1em;
        }
        
        .qr-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border: 2px dashed #e9ecef;
            text-align: center;
        }
        
        #qr-code {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
        }
        
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            font-weight: 500;
            text-align: center;
        }
        
        .status.waiting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }
        
        .status.ready {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .instructions {
            color: #6c757d;
            line-height: 1.6;
            margin-top: 20px;
            font-size: 0.9em;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #2c3e50;
        }
        
        .form-group select,
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group select:focus,
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-group small {
            display: block;
            margin-top: 5px;
            color: #6c757d;
            font-size: 12px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s, opacity 0.3s;
            font-weight: 500;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid transparent;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffeeba;
        }
        
        .config-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .config-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .provider-info {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 5px;
        }
        
        .api-key-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .api-key-available {
            background: #d4edda;
            color: #155724;
        }
        
        .api-key-missing {
            background: #f8d7da;
            color: #721c24;
        }
        
        .current-config {
            background: #e7f3ff;
            border: 1px solid #b3d4fc;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .current-config h4 {
            color: #0056b3;
            margin-bottom: 10px;
        }
        
        .config-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            font-size: 14px;
        }
        
        .config-item strong {
            color: #2c3e50;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- QR Code Panel -->
        <div class="panel">
            <h1>🤖 WhatsApp Connection</h1>
            <p class="subtitle">Scan the QR code below to connect your WhatsApp</p>
            
            <div class="qr-container">
                <div id="qr-status" class="status waiting">
                    <div class="loading"></div>
                    Waiting for QR code...
                </div>
                <img id="qr-code" style="display: none;" />
            </div>
            
            <div class="instructions">
                <strong>Instructions:</strong><br>
                1. Open WhatsApp on your phone<br>
                2. Go to Settings → Linked Devices<br>
                3. Tap "Link a Device"<br>
                4. Scan the QR code above
            </div>
            
            <button class="btn" onclick="refreshPage()">Refresh Page</button>
        </div>

        <!-- Configuration Panel -->
        <div class="panel">
            <h1>⚙️ Bot Configuration</h1>
            <p class="subtitle">Manage your bot settings without restarting</p>
            
            <div id="config-alerts"></div>
            
            <div class="current-config" id="current-config-display">
                <h4>Current Configuration</h4>
                <div id="current-config-content">Loading...</div>
            </div>
            
            <form id="config-form">
                <div class="config-section">
                    <h3>🧠 AI Provider & Model</h3>
                    
                    <div class="form-group">
                        <label for="provider">AI Provider</label>
                        <select id="provider" name="provider" required>
                            <option value="">Select Provider...</option>
                        </select>
                        <div id="provider-info" class="provider-info"></div>
                    </div>
                    
                    <div class="form-group">
                        <label for="model">Model</label>
                        <select id="model" name="model" required>
                            <option value="">Select Model...</option>
                        </select>
                        <small>Different models have different capabilities and costs</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="promptTemplate">Personality Template</label>
                        <select id="promptTemplate" name="promptTemplate" required>
                            <option value="">Select Template...</option>
                        </select>
                        <small>Choose the bot's personality and behavior style</small>
                    </div>
                    
                    <div class="form-group" id="custom-prompt-group" style="display: none;">
                        <label for="customPrompt">Custom Prompt</label>
                        <textarea id="customPrompt" name="customPrompt" rows="8" placeholder="Enter your custom prompt here. Use ${botName} as a placeholder for the bot name."></textarea>
                        <small>Write your own custom prompt. Use ${botName} where you want the bot name to appear.</small>
                    </div>
                </div>
                
                <div class="config-section">
                    <h3>🤖 Bot Identity</h3>
                    
                    <div class="form-group">
                        <label for="botName">Bot Name</label>
                        <input type="text" id="botName" name="botName" required placeholder="e.g., Nonce">
                        <small>The name the bot responds to when mentioned</small>
                    </div>
                </div>
                
                <div class="config-section">
                    <h3>⚡ Performance Settings</h3>
                      
                    <div class="form-group">
                        <label for="temperature">Temperature</label>
                        <input type="number" id="temperature" name="temperature" min="0" max="2" step="0.1" required>
                        <small>Creativity level: 0 = very focused, 2 = very creative</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="maxContextMessages">Context Messages</label>
                        <input type="number" id="maxContextMessages" name="maxContextMessages" min="1" max="50" required>
                        <small>How many previous messages to remember (1-50)</small>
                    </div>
                </div>
                
                <div class="config-section">
                    <h3>🛡️ Rate Limiting</h3>
                    
                    <div class="form-group">
                        <label for="rateLimit">Requests per Window</label>
                        <input type="number" id="rateLimit" name="rateLimit" min="1" max="100" required>
                        <small>Maximum requests allowed per time window</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="rateLimitWindow">Time Window (ms)</label>
                        <input type="number" id="rateLimitWindow" name="rateLimitWindow" min="1000" max="300000" required>
                        <small>Time window in milliseconds (1000 = 1 second)</small>
                    </div>
                </div>
                
                <div class="config-section">
                    <h3>💬 Chat Settings</h3>
                    
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="groupOnly" name="groupOnly">
                            <label for="groupOnly">Group Chat Only</label>
                        </div>
                        <small>If checked, bot only responds in group chats, not direct messages</small>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <button type="submit" class="btn">💾 Save Configuration</button>
                    <button type="button" class="btn btn-secondary" onclick="loadCurrentConfig()">🔄 Refresh</button>
                    <button type="button" class="btn btn-danger" onclick="resetConfig()">🔄 Reset to Defaults</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let isConnected = false;
        let currentConfig = {};
        let availableProviders = [];
        let availablePrompts = [];
        let apiKeys = {};
        
        // QR Code functionality
        async function checkStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                if (data.qrCode && !isConnected) {
                    document.getElementById('qr-code').src = data.qrCode;
                    document.getElementById('qr-code').style.display = 'block';
                    document.getElementById('qr-status').innerHTML = '📱 Scan this QR code with your WhatsApp';
                    document.getElementById('qr-status').className = 'status waiting';
                } else if (data.status === 'ready') {
                    document.getElementById('qr-status').innerHTML = '✅ WhatsApp connected successfully!';
                    document.getElementById('qr-status').className = 'status ready';
                    document.getElementById('qr-code').style.display = 'none';
                    isConnected = true;
                } else if (data.status === 'authenticated') {
                    document.getElementById('qr-status').innerHTML = '🔐 Authentication successful, preparing...';
                    document.getElementById('qr-status').className = 'status ready';
                }
            } catch (error) {
                console.error('Error checking status:', error);
            }
        }
        
        function refreshPage() {
            location.reload();
        }
        
        // Configuration functionality
        async function loadCurrentConfig() {
            try {
                const response = await fetch('/api/config');
                const data = await response.json();
                
                currentConfig = data.config;
                availableProviders = data.availableProviders;
                availablePrompts = data.availablePrompts;
                apiKeys = data.apiKeys;
                
                // Populate provider dropdown
                const providerSelect = document.getElementById('provider');
                providerSelect.innerHTML = '<option value="">Select Provider...</option>';
                availableProviders.forEach(provider => {
                    const option = document.createElement('option');
                    option.value = provider.id;
                    option.textContent = provider.name;
                    providerSelect.appendChild(option);
                });
                
                // Populate prompt template dropdown
                const promptSelect = document.getElementById('promptTemplate');
                promptSelect.innerHTML = '<option value="">Select Template...</option>';
                availablePrompts.forEach(prompt => {
                    const option = document.createElement('option');
                    option.value = prompt.id;
                    option.textContent = `${prompt.name} - ${prompt.description}`;
                    promptSelect.appendChild(option);
                });
                
                // Set current values
                document.getElementById('provider').value = currentConfig.provider;
                document.getElementById('promptTemplate').value = currentConfig.promptTemplate;
                document.getElementById('botName').value = currentConfig.botName;
                document.getElementById('temperature').value = currentConfig.temperature;
                document.getElementById('maxContextMessages').value = currentConfig.maxContextMessages;
                document.getElementById('rateLimit').value = currentConfig.rateLimit;
                document.getElementById('rateLimitWindow').value = currentConfig.rateLimitWindow;
                document.getElementById('groupOnly').checked = currentConfig.groupOnly;
                document.getElementById('customPrompt').value = currentConfig.customPrompt || '';
                
                // Load models for current provider
                await loadModels(currentConfig.provider);
                document.getElementById('model').value = currentConfig.model;
                
                // Update provider info
                updateProviderInfo();
                
                // Update custom prompt visibility
                toggleCustomPromptField();
                
                // Update current config display
                updateCurrentConfigDisplay();
                
            } catch (error) {
                console.error('Error loading configuration:', error);
                showAlert('Error loading configuration: ' + error.message, 'error');
            }
        }
        
        async function loadModels(provider) {
            if (!provider) return;
            
            try {
                const response = await fetch(`/api/config/models/${provider}`);
                const data = await response.json();
                
                const modelSelect = document.getElementById('model');
                modelSelect.innerHTML = '<option value="">Select Model...</option>';
                
                data.models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model;
                    option.textContent = model;
                    modelSelect.appendChild(option);
                });
                
            } catch (error) {
                console.error('Error loading models:', error);
            }
        }
        
        function updateProviderInfo() {
            const provider = document.getElementById('provider').value;
            const infoDiv = document.getElementById('provider-info');
            
            if (provider) {
                const providerData = availableProviders.find(p => p.id === provider);
                const hasKey = apiKeys[provider];
                
                infoDiv.innerHTML = `
                    <span class="api-key-status ${hasKey ? 'api-key-available' : 'api-key-missing'}">
                        ${hasKey ? '✅ API Key Available' : '❌ API Key Missing'}
                    </span>
                `;
                
                if (!hasKey) {
                    infoDiv.innerHTML += `<small>Required: ${providerData.requiresKey}</small>`;
                }
            } else {
                infoDiv.innerHTML = '';
            }
        }
        
        function updateCurrentConfigDisplay() {
            const content = document.getElementById('current-config-content');
            const provider = availableProviders.find(p => p.id === currentConfig.provider);
            const prompt = availablePrompts.find(p => p.id === currentConfig.promptTemplate);
            
            content.innerHTML = `
                <div class="config-item"><strong>Provider:</strong> <span>${provider ? provider.name : currentConfig.provider}</span></div>
                <div class="config-item"><strong>Model:</strong> <span>${currentConfig.model}</span></div>
                <div class="config-item"><strong>Bot Name:</strong> <span>${currentConfig.botName}</span></div>
                <div class="config-item"><strong>Prompt:</strong> <span>${prompt ? prompt.name : currentConfig.promptTemplate}</span></div>
                <div class="config-item"><strong>Temperature:</strong> <span>${currentConfig.temperature}</span></div>
                <div class="config-item"><strong>Group Only:</strong> <span>${currentConfig.groupOnly ? 'Yes' : 'No'}</span></div>
            `;
        }
        
        function toggleCustomPromptField() {
            const promptTemplate = document.getElementById('promptTemplate').value;
            const customPromptGroup = document.getElementById('custom-prompt-group');
            
            if (promptTemplate === 'custom') {
                customPromptGroup.style.display = 'block';
            } else {
                customPromptGroup.style.display = 'none';
            }
        }
        
        function showAlert(message, type = 'success') {
            const alertsDiv = document.getElementById('config-alerts');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            
            alertsDiv.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }
        
        async function resetConfig() {
            if (!confirm('Are you sure you want to reset all configuration to defaults? This cannot be undone.')) {
                return;
            }
            
            try {
                const response = await fetch('/api/config/reset', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showAlert('Configuration reset to defaults successfully!', 'success');
                    await loadCurrentConfig();
                } else {
                    showAlert('Error resetting configuration: ' + data.error, 'error');
                }
            } catch (error) {
                showAlert('Error resetting configuration: ' + error.message, 'error');
            }
        }
        
        // Event listeners
        document.getElementById('provider').addEventListener('change', function() {
            loadModels(this.value);
            updateProviderInfo();
        });
        
        document.getElementById('promptTemplate').addEventListener('change', function() {
            toggleCustomPromptField();
        });
        
        document.getElementById('config-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const config = {
                provider: formData.get('provider'),
                model: formData.get('model'),
                promptTemplate: formData.get('promptTemplate'),
                botName: formData.get('botName'),
                temperature: parseFloat(formData.get('temperature')),
                maxContextMessages: parseInt(formData.get('maxContextMessages')),
                rateLimit: parseInt(formData.get('rateLimit')),
                rateLimitWindow: parseInt(formData.get('rateLimitWindow')),
                groupOnly: formData.has('groupOnly'),
                customPrompt: formData.get('customPrompt')
            };
            
            try {
                const response = await fetch('/api/config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(config)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showAlert('Configuration updated successfully! Changes are active immediately.', 'success');
                    await loadCurrentConfig();
                } else {
                    showAlert('Error updating configuration: ' + (data.details ? data.details.join(', ') : data.error), 'error');
                }
            } catch (error) {
                showAlert('Error updating configuration: ' + error.message, 'error');
            }
        });
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadCurrentConfig();
            checkStatus();
            
            // Check status every 2 seconds for QR code
            setInterval(checkStatus, 2000);
            
            // Refresh config every 30 seconds to stay in sync
            setInterval(loadCurrentConfig, 30000);
        });
    </script>
</body>
</html> 