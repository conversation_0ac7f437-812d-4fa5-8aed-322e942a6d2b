const { createGroq } = require("@ai-sdk/groq");
const { createGoogleGenerativeAI } = require("@ai-sdk/google");
const {
  getNodeAutoInstrumentations,
} = require("@opentelemetry/auto-instrumentations-node");
const { NodeSDK } = require("@opentelemetry/sdk-node");
const {
  generateText: aiGenerateText,
  experimental_createMCPClient: createMCPClient,
} = require("ai");
const { Experimental_StdioMCPTransport } = require("ai/mcp-stdio");
const { LangfuseExporter } = require("langfuse-vercel");
const { configService } = require("./configService");
const logger = require("../utils/logger");

const groq = createGroq({
  baseURL: "https://api.supermemory.ai/v3/https://api.groq.com/openai/v1",
});

const google = createGoogleGenerativeAI({
  baseURL:
    "https://api.supermemory.ai/v3/https://generativelanguage.googleapis.com/v1beta/openai/",
});

// Initialize SDK once at module load time
const sdk = new NodeSDK({
  traceExporter: new LangfuseExporter(),
  instrumentations: [getNodeAutoInstrumentations()],
});

// Start the SDK once
sdk.start();

// Handle graceful shutdown
process.on("SIGTERM", async () => {
  await sdk.shutdown();
});

process.on("SIGINT", async () => {
  await sdk.shutdown();
});

async function generateText(messages) {
  // Get current configuration
  const provider = configService.get("provider");
  const model = configService.get("model");
  const temperature = configService.get("temperature");
  let memoryMcpClient;
  let searchMcpClient;

  try {
    memoryMcpClient = await createMCPClient({
      transport: {
        type: "sse",
        url: "https://mcp.supermemory.ai/6Wy3fW0xlIJJTSIKN7C9H/sse",
      },
    });

    const transport = new Experimental_StdioMCPTransport({
      command: "npx",
      args: [
        "-y",
        "exa-mcp-server",
        "--tools=web_search_exa,company_research,crawling,linkedin_search",
      ],
      headers: {
        Authorization: "4e01cf2d-7b82-4ee3-ad97-21b7fd805e78",
      },
      env: {
        EXA_API_KEY: "4e01cf2d-7b82-4ee3-ad97-21b7fd805e78",
      },
    });
    searchMcpClient = await createMCPClient({
      transport,
    });

    const toolSetOne = await memoryMcpClient.tools();
    const toolSetTwo = await searchMcpClient.tools();
    const tools = {
      ...toolSetOne,
      ...toolSetTwo,
    };

    // Build the config object
    const config = {
      model:
        provider === "groq"
          ? groq(model)
          : google(
              model
              //  {
              //   useSearchGrounding: true,
              //   dynamicRetrievalConfig: {
              //     mode: "MODE_DYNAMIC",
              //     dynamicThreshold: 0.8,
              //   },
              // }
            ),
      messages: messages,
      tools,
      maxSteps: 8,
      temperature: temperature,
      thinkingConfig: {
        thinkingBudget: 0,
      },
      experimental_telemetry: {
        isEnabled: true,
        functionId: "generateText",
      },
      headers: {
        "x-supermemory-api-key":
          "sm_BZcQUBzbU1YDXix6hdEpop_OwfUzOyMYYyBWXTtnkNzwTTqapbRuaKjQdPnjtiOFQRsgZpPVNfHrfcqCQrMJBBG",
        "x-sm-user-id": "user_123",
        "x-sm-conversation-id": "conversation-id",
      },
    };

    const result = await aiGenerateText(config);
    const allToolCalls = result?.steps?.flatMap((step) => step.toolCalls);
    logger.info(`Total tool calls: ${allToolCalls.length}`);

    return result.text;
  } finally {
    await memoryMcpClient?.close();
    await searchMcpClient?.close();
  }
}

module.exports = {
  generateText,
};
