const { GoogleGenAI, Modality } = require("@google/genai");

async function generateImageGemini(prompt, imageBuffer = null) {
  const ai = new GoogleGenAI({
    apiKey: process.env.GOOGLE_GENERATIVE_AI_API_KEY,
  });

  // Prepare contents array
  let contents = [];

  if (imageBuffer) {
    // For image editing, include both the image and the text prompt
    contents = [
      {
        parts: [
          {
            inlineData: {
              mimeType: "image/png",
              data: imageBuffer.toString("base64"),
            },
          },
          {
            text: prompt,
          },
        ],
      },
    ];
  } else {
    // For text-to-image generation, just use the text prompt
    contents = prompt;
  }

  try {
    // Set responseModalities to include "Image" so the model can generate an image
    const response = await ai.models.generateContent({
      model: "gemini-2.0-flash-preview-image-generation",
      contents: contents,
      config: {
        responseModalities: [Modality.TEXT, Modality.IMAGE],
      },
    });

    // Log the response structure for debugging
    console.log("Response structure:", JSON.stringify(response, null, 2));

    // Check if response has the expected structure
    if (!response || !response.candidates || !response.candidates[0]) {
      console.log("Invalid response structure - no candidates found");
      return null;
    }

    const candidate = response.candidates[0];
    if (!candidate.content || !candidate.content.parts) {
      console.log("Invalid response structure - no content parts found");
      return null;
    }

    for (const part of candidate.content.parts) {
      // Based on the part type, either show the text or return the image buffer
      if (part.text) {
        console.log(part.text);
      } else if (part.inlineData) {
        const imageData = part.inlineData.data;
        if (!imageData) {
          console.log("No image data found");
          return null;
        }
        const buffer = Buffer.from(imageData, "base64");
        console.log("Image generated successfully!");
        return buffer;
      }
    }

    // If no image was generated, return null
    return null;
  } catch (error) {
    console.error("Error in generateImageGemini:", error);
    throw error;
  }
}

module.exports = {
  generateImageGemini,
};
