const logger = require("../utils/logger");

// Slash Commands Configuration
const SLASH_COMMANDS = {
  voice: {
    aliases: ["/voice", "/speak", "/say", "/tts"],
    type: "voice",
    description: "Generate speech from text",
    example: "/voice Hello, how are you?",
    helpText: "Please provide text after the command.",
  },
  image: {
    aliases: ["/image", "/img", "/generate", "/draw", "/create"],
    type: "image_generation",
    description: "Generate an image from description",
    example: "/image a sunset over mountains",
    helpText: "Please provide an image description after the command.",
  },
  edit: {
    aliases: ["/edit", "/modify", "/transform"],
    type: "image_editing",
    description: "Edit an existing image",
    example: "/edit make it black and white",
    helpText:
      "Please provide editing instructions and make sure to include an image.",
  },
  resetMemory: {
    aliases: ["/resetMemory", "/reset", "/clearMemory", "/forget"],
    type: "reset_memory",
    description: "Reset conversation memory from this point forward",
    example: "/resetMemory",
    helpText:
      "This command clears the conversation context. No additional text needed.",
  },
  help: {
    aliases: ["/help", "/commands", "/h"],
    type: "help",
    description: "Show available commands",
    example: "/help",
    helpText: "Shows all available slash commands.",
  },
};

/**
 * Helper function to get command info from message
 * @param {string} messageBody - The message body to parse
 * @returns {object|null} Command info object or null if no command found
 */
function getSlashCommandInfo(messageBody) {
  const trimmedMessage = messageBody.trim();

  for (const [commandKey, commandConfig] of Object.entries(SLASH_COMMANDS)) {
    for (const alias of commandConfig.aliases) {
      if (trimmedMessage.startsWith(`${alias} `) || trimmedMessage === alias) {
        return {
          key: commandKey,
          config: commandConfig,
          alias: alias,
          content: trimmedMessage.substring(alias.length).trim(),
        };
      }
    }
  }

  return null;
}

/**
 * Process slash command and return command details
 * @param {object} message - WhatsApp message object
 * @param {string} messageBody - The message body
 * @returns {object} Command processing result
 */
async function processSlashCommand(message, messageBody) {
  const slashCommandInfo = getSlashCommandInfo(messageBody);

  if (!slashCommandInfo) {
    return { isSlashCommand: false };
  }

  const { key, config, alias, content } = slashCommandInfo;

  // Help and resetMemory commands don't require content
  if (!content && config.type !== "help" && config.type !== "reset_memory") {
    await message.reply(`${config.helpText} Example: \`${config.example}\``);
    return { isSlashCommand: true, handled: true };
  }

  const result = {
    isSlashCommand: true,
    handled: false,
    question: content,
    isVoiceNote: false,
    isImageGeneration: false,
    isImageEditing: false,
  };

  switch (config.type) {
    case "voice": {
      result.isVoiceNote = true;
      logger.info(`Slash command ${alias} (voice) detected: "${content}"`);
      break;
    }
    case "image_generation": {
      result.isImageGeneration = true;
      logger.info(
        `Slash command ${alias} (image generation) detected: "${content}"`
      );
      break;
    }
    case "image_editing": {
      result.isImageEditing = true;
      logger.info(
        `Slash command ${alias} (image editing) detected: "${content}"`
      );
      break;
    }
    case "reset_memory": {
      logger.info(`Reset memory command ${alias} requested`);

      await message.reply("🧠 Memory reset. I am now lobotomised");
      result.handled = true;
      break;
    }
    case "help": {
      logger.info(`Help command ${alias} requested`);

      // Generate help message
      let helpMessage = "🤖 *Available Commands:*\n\n";

      for (const [commandKey, commandConfig] of Object.entries(
        SLASH_COMMANDS
      )) {
        if (commandKey === "help") continue; // Skip help command in the list

        helpMessage += `*${commandConfig.description}*\n`;
        helpMessage += `Commands: ${commandConfig.aliases.join(", ")}\n`;
        helpMessage += `Example: \`${commandConfig.example}\`\n\n`;
      }

      await message.reply(helpMessage);
      result.handled = true;
      break;
    }
    default: {
      logger.warn(`Unknown command type: ${config.type}`);
      await message.reply("Sorry, I don't recognize that command type.");
      result.handled = true;
      break;
    }
  }

  return result;
}

/**
 * Check if a message contains a slash command
 * @param {string} messageBody - The message body to check
 * @returns {boolean} True if message contains a slash command
 */
function isSlashCommand(messageBody) {
  return getSlashCommandInfo(messageBody) !== null;
}

/**
 * Get all available slash commands
 * @returns {object} All slash commands configuration
 */
function getAllSlashCommands() {
  return SLASH_COMMANDS;
}

module.exports = {
  getSlashCommandInfo,
  processSlashCommand,
  isSlashCommand,
  getAllSlashCommands,
  SLASH_COMMANDS,
};
