const logger = require("../utils/logger");
const {
  getPersonaPrompt,
  getTuringPrompt,
  getTuringPrompt2,
  getTuringPrompt3,
  getPromptByTemplate,
} = require("../prompts/turingPrompt");
const { generateText } = require("./generateText");
const { configService } = require("./configService");

/**
 * Generates a response using the Vercel AI SDK with Groq/Gemini
 * @param {string} prompt - The user's question or prompt
 * @param {Array} context - Array of previous messages for context (optional)
 * @param {Buffer} imageBuffer - Image data as Buffer (optional, for vision models)
 * @param {Buffer} audioBuffer - Audio data as Buffer (optional, for audio processing)
 * @param {string} audioMimeType - MIME type of the audio file (optional)
 * @param {string} userId - WhatsApp user ID for tracking (optional)
 * @param {string} conversationId - WhatsApp chat ID for tracking (optional)
 * @returns {Promise<string>} - The generated response
 */
const generateResponse = async (
  prompt,
  context = [],
  imageBuffer = null,
  audioBuffer = null,
  audioMimeType = null,
  userId = null,
  conversationId = null
) => {
  if (!process.env.GROQ_API_KEY && !process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
    throw new Error(
      "Groq or Gemini API key is not set. Please set GROQ_API_KEY or GOOGLE_GENERATIVE_AI_API_KEY in your .env file."
    );
  }

  try {
    // Get current configuration
    const botName = configService.get("botName");
    const promptTemplate = configService.get("promptTemplate");
    const customPrompt = configService.get("customPrompt");

    const systemPrompt = getPromptByTemplate(
      promptTemplate,
      botName,
      customPrompt
    );

    // Build messages array with context
    const messages = [
      {
        role: "system",
        content: systemPrompt,
      },
    ];

    // Add context messages if provided
    if (context && context.length > 0) {
      const contextMessages = context.map((msg) => ({
        role: msg.fromMe ? "assistant" : "user",
        content: msg.content,
      }));
      messages.push(...contextMessages);
    }

    // Add the current user message (with optional image and/or audio)
    if (imageBuffer || audioBuffer) {
      // Create multimodal message with text and media
      const contentParts = [
        {
          type: "text",
          text: prompt,
        },
      ];

      if (imageBuffer) {
        contentParts.push({
          type: "image",
          image: imageBuffer,
        });
      }

      if (audioBuffer) {
        contentParts.push({
          type: "file",
          data: audioBuffer,
          mimeType: audioMimeType,
        });
      }

      messages.push({
        role: "user",
        content: contentParts,
      });
    } else {
      // Text-only message
      messages.push({
        role: "user",
        content: prompt,
      });
    }

    const text = await generateText(messages, userId, conversationId);

    return text.trim();
  } catch (error) {
    logger.error("Error generating response from LLM:", error);
    throw new Error("Failed to generate a response. Please try again later.");
  }
};

module.exports = {
  generateResponse,
};
