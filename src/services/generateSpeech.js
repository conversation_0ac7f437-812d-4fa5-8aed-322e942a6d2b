const { experimental_generateSpeech } = require("ai");
const { openai } = require("@ai-sdk/openai");
const { Buffer } = require("node:buffer");
const { GoogleGenAI } = require("@google/genai");
const lamejs = require("lamejs");
const { keyRotator } = require("../utils/keyRotator");

// async function generateSpeechFromTextOld(text, voice = "alloy") {
//   if (!process.env.OPENAI_API_KEY) {
//     throw new Error(
//       "OpenAI API key is not set. Please set OPENAI_API_KEY in your .env file."
//     );
//   }

//   const speechResult = await experimental_generateSpeech({
//     model: openai.speech("gpt-4o-mini-tts"),
//     text: text,
//     voice: voice,
//   });

//   const audioBuffer = Buffer.from(speechResult.audio.uint8Array);
//   return {
//     audioBuffer,
//     contentType: speechResult.audio.mimeType,
//   };
// }

/**
 * Detects voice style requirements (accent, tone, pace, style) from user input
 * @param {string} userMessage - The original user message requesting voice
 * @returns {string|null} - The detected style instruction or null if none found
 */
function detectVoiceStyleRequirements(userMessage) {
  if (!userMessage) return null;

  const message = userMessage.toLowerCase();

  // Patterns for different voice style requirements
  const stylePatterns = [
    // Accents
    /\b(?:in\s+(?:an?\s+)?|with\s+(?:an?\s+)?|using\s+(?:an?\s+)?)?(indian|british|american|australian|scottish|irish|french|german|italian|spanish|russian|chinese|japanese|southern|new\s+york|california|texas|boston)\s+accent\b/,

    // Tones and emotions
    /\b(?:in\s+(?:a\s+)?|with\s+(?:a\s+)?|using\s+(?:a\s+)?)?(happy|sad|angry|excited|calm|serious|playful|dramatic|mysterious|scary|cheerful|depressed|enthusiastic|bored|surprised|confused|confident|nervous|romantic|sarcastic|harsh|gentle|soft|loud|quiet|whispered|shouted)\s+(?:voice|tone)\b/,

    // Speaking pace
    /\b(?:speak|say|talk)(?:ing)?\s+(?:very\s+)?(slowly|quickly|fast|slow|rapidly|gradually)\b/,
    /\b(?:in\s+(?:a\s+)?|with\s+(?:a\s+)?)?(slow|fast|quick|rapid|gradual)\s+(?:pace|speed|tempo)\b/,

    // Speaking style
    /\b(?:in\s+(?:a\s+)?|with\s+(?:a\s+)?|using\s+(?:a\s+)?)?(robotic|monotone|expressive|animated|theatrical|professional|casual|formal|informal|childlike|elderly|deep|high-pitched|raspy|smooth|crisp|clear)\s+(?:voice|style|manner)\b/,

    // Specific voice characteristics
    /\b(?:like\s+(?:a\s+)?|as\s+(?:a\s+)?|in\s+the\s+style\s+of\s+(?:a\s+)?)(robot|child|old\s+person|narrator|announcer|news\s+anchor|teacher|pirate|cowboy|villain|hero|princess|king|queen)\b/,
  ];

  for (const pattern of stylePatterns) {
    const match = message.match(pattern);
    if (match) {
      // Return the full matched phrase, cleaned up
      return match[0].replace(/\b(in|with|using|an?|a|the)\s+/g, "").trim();
    }
  }

  return null;
}

/**
 * Generates speech from text using the Gemini API and converts to MP3 format.
 *
 * @param {string} text The text to convert to speech.
 * @param {string} userMessage Optional original user message to detect style requirements
 * @returns {Promise<{audioBuffer: Buffer, contentType: string}>} A promise that resolves to an object
 * containing the MP3 audio buffer and the content type.
 */
async function generateSpeechFromText(text, userMessage = null) {
  // Detect voice style requirements from user message
  const styleRequirement = userMessage
    ? detectVoiceStyleRequirements(userMessage)
    : null;

  // Prefix style requirement to the text if detected
  let processedText = text;
  if (styleRequirement) {
    processedText = `Read this text ${styleRequirement}: ${text}`;
  }

  // Replace 'lol' with '[laughs]' (case-insensitive)
  processedText = processedText.replace(/\blol\b/gi, "[laughs]");

  return await keyRotator.executeWithRotation("gemini", async (apiKey) => {
    const genAI = new GoogleGenAI(apiKey);

    const speechResult = await genAI.models.generateContent({
      model: "gemini-2.5-flash-preview-tts",
      contents: [{ parts: [{ text: processedText }] }],
      config: {
        responseModalities: ["AUDIO"],
        speechConfig: {
          voiceConfig: {
            prebuiltVoiceConfig: {
              voiceName: "Kore",
            },
          },
        },
      },
    });

    // The audio data is returned as a base64 encoded string in the Node.js SDK.
    const data =
      speechResult.candidates?.[0]?.content?.parts?.[0]?.inlineData?.data;
    const rawAudioBuffer = Buffer.from(data, "base64");

    // Convert raw PCM audio to MP3 format
    const mp3Buffer = convertPCMToMP3(rawAudioBuffer);

    return {
      audioBuffer: mp3Buffer,
      contentType: "audio/mpeg",
    };
  });
}

/**
 * Converts raw PCM audio data to MP3 format using lamejs.
 *
 * @param {Buffer} pcmBuffer The raw PCM audio data buffer.
 * @returns {Buffer} The MP3 audio buffer.
 */
function convertPCMToMP3(pcmBuffer) {
  // Convert buffer to 16-bit signed integer array
  const samples = new Int16Array(
    pcmBuffer.buffer,
    pcmBuffer.byteOffset,
    pcmBuffer.length / 2
  );

  // Initialize MP3 encoder
  // Gemini TTS typically outputs at 24kHz, mono
  const mp3encoder = new lamejs.Mp3Encoder(1, 24000, 128); // 1 channel, 24kHz, 128kbps

  const mp3Data = [];
  const sampleBlockSize = 1152; // Standard MP3 frame size

  // Process audio in chunks
  for (let i = 0; i < samples.length; i += sampleBlockSize) {
    const sampleChunk = samples.subarray(i, i + sampleBlockSize);
    const mp3buf = mp3encoder.encodeBuffer(sampleChunk);
    if (mp3buf.length > 0) {
      mp3Data.push(Buffer.from(mp3buf));
    }
  }

  // Flush remaining data
  const mp3buf = mp3encoder.flush();
  if (mp3buf.length > 0) {
    mp3Data.push(Buffer.from(mp3buf));
  }

  return Buffer.concat(mp3Data);
}

module.exports = {
  generateSpeechFromText,
  detectVoiceStyleRequirements,
};
