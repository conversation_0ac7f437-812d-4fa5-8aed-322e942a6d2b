FROM node:24-slim

# Install required dependencies for Puppeteer (from whatsapp-web.js docs)
RUN apt-get update && apt-get install -y \
    chromium \
    gconf-service \
    libgbm-dev \
    libasound2 \
    libatk1.0-0 \
    libc6 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libexpat1 \
    libfontconfig1 \
    libgcc1 \
    libgconf-2-4 \
    libgdk-pixbuf2.0-0 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libstdc++6 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxss1 \
    libxtst6 \
    ca-certificates \
    fonts-liberation \
    libappindicator1 \
    libnss3 \
    lsb-release \
    xdg-utils \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Set environment variable for Puppeteer to use Chromium
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium

# Create app directory
WORKDIR /usr/src/app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy app source
COPY . .

# Create startup script to clean up any stale processes
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
echo "Starting cleanup process..."\n\
\n\
# Kill any existing browser processes more aggressively\n\
pkill -9 -f chromium || true\n\
pkill -9 -f chrome || true\n\
pkill -9 -f google-chrome || true\n\
pkill -9 -f puppeteer || true\n\
killall chromium || true\n\
killall chrome || true\n\
killall google-chrome || true\n\
\n\
# Wait for processes to die\n\
sleep 2\n\
\n\
# Comprehensive cleanup of all Chrome-related directories\n\
rm -rf /tmp/chrome-user-data* || true\n\
rm -rf /tmp/.org.chromium.* || true\n\
rm -rf /tmp/.com.google.Chrome.* || true\n\
rm -rf /root/.config/chromium* || true\n\
rm -rf /root/.config/google-chrome* || true\n\
rm -rf /root/.cache/chromium* || true\n\
rm -rf /root/.cache/google-chrome* || true\n\
rm -rf /home/<USER>/.config/chromium* || true\n\
rm -rf /home/<USER>/.config/google-chrome* || true\n\
rm -rf /home/<USER>/.cache/chromium* || true\n\
rm -rf /home/<USER>/.cache/google-chrome* || true\n\
rm -rf /home/<USER>/.config/chromium* || true\n\
rm -rf /home/<USER>/.config/google-chrome* || true\n\
\n\
# Clean up Chrome browser locks in WhatsApp auth directory (but preserve WhatsApp auth)\n\
find /usr/src/app/.wwebjs_auth -name "*SingletonLock*" -delete 2>/dev/null || true\n\
\n\
# Clean up lock files more comprehensively\n\
find /tmp -name "*SingletonLock*" -delete 2>/dev/null || true\n\
find /tmp -name "*singleton*" -delete 2>/dev/null || true\n\
find /tmp -name "*.lock" -delete 2>/dev/null || true\n\
find /root -name "*SingletonLock*" -delete 2>/dev/null || true\n\
find /root -name "*singleton*" -delete 2>/dev/null || true\n\
find /home -name "*SingletonLock*" -delete 2>/dev/null || true\n\
find /home -name "*singleton*" -delete 2>/dev/null || true\n\
\n\
# Clean up any shared memory segments\n\
ipcs -m | awk '"'"'/chrome|chromium/ { print $2 }'"'"' | xargs -r ipcrm -m || true\n\
\n\
# Final wait to ensure everything is clean\n\
sleep 1\n\
\n\
echo "Cleanup completed, starting application..."\n\
\n\
# Start the application\n\
exec "$@"' > /usr/src/app/entrypoint.sh && chmod +x /usr/src/app/entrypoint.sh

ENTRYPOINT ["/usr/src/app/entrypoint.sh"]
CMD ["npm", "start"]