{"name": "whatsapp-llm-bot", "version": "1.0.0", "description": "A WhatsApp bot that responds to mentions with LLM-generated content", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "tsx src/index.js", "dev2": "<PERSON>ra dev", "start:v2": "tsx src/v2/index.ts", "dev:v2": "tsx watch src/v2/index.ts", "build:v2": "tsc -p tsconfig.json", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@ai-sdk/google": "^1.2.19", "@ai-sdk/groq": "^1.2.9", "@ai-sdk/openai": "^1.3.22", "@google/genai": "^1.4.0", "@mastra/core": "^0.10.5", "@mastra/fastembed": "^0.10.0", "@mastra/libsql": "^0.10.2", "@mastra/loggers": "^0.10.2", "@mastra/memory": "^0.10.3", "@opentelemetry/auto-instrumentations-node": "^0.60.0", "@opentelemetry/sdk-node": "^0.202.0", "ai": "^4.3.16", "chalk": "^4.1.2", "dotenv": "^16.5.0", "express": "^5.1.0", "https-proxy-agent": "^7.0.6", "lamejs": "github:zhuker/lamejs", "langfuse-vercel": "^3.37.4", "node-cache": "^5.1.2", "node-fetch": "^3.3.2", "openai-gpt-token-counter": "^1.1.1", "puppeteer": "^24.10.1", "qrcode": "^1.5.4", "qrcode-terminal": "^0.12.0", "whatsapp-web.js": "^1.30.0", "zod": "^3.25.64"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/node": "^24.0.2", "mastra": "^0.10.5", "@types/qrcode": "^1.5.5", "@types/qrcode-terminal": "^0.12.2", "tsx": "^4.19.0", "typescript": "^5.8.3"}}